DEBUGGING FLASK API CONNECTION ISSUES

If you're seeing 404 errors when connecting to your Flask API, here are some steps to debug:

1. VERIFY THE FLASK API ENDPOINT

   Run this Python code in your Flask application to print out all registered routes:
   
   ```python
   # Add this to the bottom of your Flask app file
   if __name__ == '__main__':
       print("REGISTERED ROUTES:")
       for rule in app.url_map.iter_rules():
           print(f"{rule.endpoint}: {rule}")
       app.run(debug=True)
   ```

   This will show you exactly what endpoints are available in your Flask API.

2. CHECK THE FLASK SERVER LOGS

   Look at your Flask server console output to see what endpoints are being requested
   and what responses are being sent.

3. COMMON ENDPOINT FORMATS TO TRY:

   If your endpoint isn't `/predict/<days>`, it might be one of these:
   
   - `/predict?days=30` (query parameter)
   - `/api/predict/30` (with API prefix)
   - `/v1/predict/30` (with version prefix)
   - `/prediction/30` (different name)

4. CORS SOLUTION:

   Make sure to add CORS support to your Flask API:
   
   ```python
   from flask_cors import CORS
   app = Flask(__name__)
   CORS(app)  # Enable CORS for all routes
   ```

5. TEST THE API DIRECTLY:

   Use a tool like Postman or curl to test the API directly:
   
   ```
   curl http://localhost:5000/predict/30
   ```

   If this works but your React app doesn't, it's likely a CORS or proxy issue.

6. CORS HEADERS FOR MANUAL DEBUGGING:

   You can manually add CORS headers to check if that's the issue:
   
   ```python
   @app.after_request
   def after_request(response):
       response.headers.add('Access-Control-Allow-Origin', '*')
       response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
       response.headers.add('Access-Control-Allow-Methods', 'GET,POST')
       return response
   ```
   
7. PROXY SETTINGS:

   React's development server should forward requests to your Flask API, but this might
   not work behind a corporate proxy. Try setting the USE_DIRECT_API flag to true in
   the predictionApi.ts file to bypass the React proxy. 