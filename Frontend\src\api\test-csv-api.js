// Simple test script to verify CSV API functionality
// Run this in browser console to test the CSV loading

import { fetchPredictionFromCSV, checkCSVDataAvailability, getDataStatistics } from './csvPredictionApi';

// Test function to verify CSV API
window.testCSVAPI = async function() {
  console.log('=== Testing CSV Prediction API ===');
  
  try {
    // Test 1: Check CSV availability
    console.log('\n1. Checking CSV data availability...');
    const isAvailable = await checkCSVDataAvailability();
    console.log('CSV available:', isAvailable);
    
    if (!isAvailable) {
      console.log('❌ CSV file not found. Please run: python AI/bitcoin_price_prediction_using_lstm.py');
      return;
    }
    
    // Test 2: Get data statistics
    console.log('\n2. Getting data statistics...');
    const stats = await getDataStatistics();
    console.log('Total predictions:', stats.totalPredictions);
    console.log('Date range:', stats.dateRange);
    
    // Test 3: Test different timeframes
    const timeframes = [30, 180, 365, 1095, 1825];
    
    for (const days of timeframes) {
      console.log(`\n3.${timeframes.indexOf(days) + 1}. Testing ${days} days prediction...`);
      
      try {
        const data = await fetchPredictionFromCSV(days);
        const key = `${days}_days`;
        
        if (data[key]) {
          console.log(`✓ Successfully loaded ${data[key].dates.length} predictions`);
          console.log(`  First date: ${data[key].dates[0]}`);
          console.log(`  Last date: ${data[key].dates[data[key].dates.length - 1]}`);
          console.log(`  First price: $${data[key].predicted_prices[0]}`);
          console.log(`  Last price: $${data[key].predicted_prices[data[key].predicted_prices.length - 1]}`);
        } else {
          console.log(`❌ No data found for ${days} days`);
        }
      } catch (error) {
        console.log(`❌ Error loading ${days} days:`, error.message);
      }
    }
    
    console.log('\n=== CSV API Test Complete ===');
    console.log('✓ All tests passed! The CSV API is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Auto-run test if in development mode
if (process.env.NODE_ENV === 'development') {
  console.log('CSV API test function available: testCSVAPI()');
}
