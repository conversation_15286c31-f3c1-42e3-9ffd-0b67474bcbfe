# Bitcoin Price Prediction System

A comprehensive Bitcoin price prediction system using an Advanced Multivariate LSTM neural network with a modern web interface. The system generates 5-year Bitcoin price predictions and serves them directly to the frontend via CSV files, eliminating the need for a backend API server.

## System Overview

This streamlined system consists of:
1. **AI Training Script**: Advanced Multivariate LSTM model that generates predictions
2. **CSV Data Pipeline**: Direct file-based data transfer to frontend
3. **React Frontend**: Interactive charts reading predictions from CSV files

**Key Innovation**: No backend server required - predictions are generated once and served via CSV files.

## Project Structure

```
hackthon_hnt_team/
├── AI/
│   ├── bitcoin_price_prediction_using_lstm.py  # Main training script
│   └── model/                                   # Trained models and scalers
│       ├── bitcoin_advanced_multivariate_lstm.keras
│       ├── bitcoin_price_scaler.save
│       ├── bitcoin_volume_scaler.save
│       └── *.png                               # Visualization plots
├── Data/
│   ├── Bitcoin Historical Data.csv             # Historical Bitcoin data
│   └── bitcoin_predictions.csv                 # Generated predictions
├── Frontend/
│   ├── src/
│   │   ├── components/BitcoinPriceChart.tsx    # Main chart component
│   │   └── api/csvPredictionApi.ts             # CSV data loading
│   └── public/Data/bitcoin_predictions.csv    # Frontend data copy
├── SYSTEM_ARCHITECTURE.md                      # Detailed architecture docs
└── README.md                                   # This file
```

## Key Features

### AI Model Capabilities
- **Advanced Multivariate LSTM**: Uses Price + Volume data for enhanced accuracy
- **5-Year Predictions**: Generates 1825 days of future Bitcoin price predictions
- **3-Step Training Process**: Validation → Full training → Prediction generation
- **Comprehensive Evaluation**: RMSE, MAE, visualization plots
- **Automatic CSV Generation**: Creates prediction data for frontend consumption

### Frontend Features
- **Interactive Charts**: ECharts-powered visualization with zoom and pan
- **5 Timeframe Views**: 1M (30d), 6M (180d), 1Y (365d), 3Y (1095d), 5Y (1825d)
- **Direct CSV Loading**: Fast, no-API data access
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark Bitcoin Theme**: Professional UI with Bitcoin-themed styling
- **Smart Error Handling**: Clear instructions when data is missing

### System Advantages
- **No Backend Server**: Direct CSV-to-frontend data flow
- **High Performance**: No API latency, instant data loading
- **Simple Deployment**: Just run training script and start frontend
- **Easy Updates**: Re-run training script to refresh predictions
- **Offline Capable**: Frontend works without internet connection

## Tech Stack

### AI Training
- Python 3.8+ (recommended: 3.11 or lower)
- TensorFlow 2.x for Advanced Multivariate LSTM
- NumPy, pandas for data manipulation
- scikit-learn for model evaluation
- matplotlib for visualizations
- joblib for model persistence

### Frontend
- React with TypeScript
- ECharts for interactive data visualization
- Styled Components for styling
- Direct CSV file reading (no API dependencies)

## Quick Start

### Prerequisites

1. Python 3.8+ (recommended: 3.11 or lower)
2. Node.js and npm
3. Git

### Step 1: Generate Predictions

```bash
# Clone the repository
git clone <repository-url>
cd hackthon_hnt_team

# Install Python dependencies
pip install tensorflow numpy pandas scikit-learn joblib matplotlib

# Train model and generate 5-year predictions
python AI/bitcoin_price_prediction_using_lstm.py
```

This will:
- Train the Advanced Multivariate LSTM model
- Generate 1825 days (5 years) of Bitcoin price predictions
- Save predictions to `Data/bitcoin_predictions.csv`
- Copy the CSV to the frontend public folder

### Step 2: Launch Frontend

```bash
# Install Node.js dependencies
cd Frontend
npm install

# Start the development server
npm start
```

### Step 3: View Predictions
Open http://localhost:3000 to view interactive Bitcoin price prediction charts.
## Data Format

### CSV Prediction File
The system generates predictions in a simple CSV format:

```csv
Date,Predicted_Price
2025-06-11,110000.00
2025-06-12,110500.00
2025-06-13,111000.00
...
```

- **File Location**: `Data/bitcoin_predictions.csv` (and copied to `Frontend/public/Data/`)
- **Total Records**: 1825 (5 years of daily predictions)
- **Date Format**: YYYY-MM-DD
- **Price Format**: USD with 2 decimal places

### Timeframe Support
The frontend supports multiple prediction timeframes:
- **1M**: 30 days
- **6M**: 180 days
- **1Y**: 365 days
- **3Y**: 1095 days
- **5Y**: 1825 days (full dataset)

## Model Architecture

### Advanced Multivariate LSTM
- **Input Features**: Price + Volume (normalized with MinMaxScaler)
- **Architecture**:
  - LSTM Layer 1: 64 units, return_sequences=True
  - LSTM Layer 2: 64 units, return_sequences=True
  - LSTM Layer 3: 64 units
  - Dense Layer: 32 units (ReLU activation)
  - Output Layer: 1 unit (price prediction)
- **Regularization**: Dropout (0.2) after each LSTM layer
- **Optimizer**: Nadam with learning rate reduction
- **Window Size**: 60 days for sequence prediction

### Training Process
1. **Validation Phase**: Test on latest 3 years of data
2. **Final Training**: Use complete historical dataset
3. **Prediction Generation**: Create 5-year future forecasts with volatility modeling

## Usage Examples

### Updating Predictions
```bash
# Generate new predictions with latest data
python AI/bitcoin_price_prediction_using_lstm.py

# The script automatically copies the CSV to the frontend
# Refresh the browser to see updated predictions
```

### Frontend Development
```bash
# Start development server
cd Frontend
npm start

# Test CSV API in browser console
testCSVAPI()
```

## System Workflow

1. **Run Training Script**: `python AI/bitcoin_price_prediction_using_lstm.py`
2. **Model Training**: Advanced LSTM trains on historical data
3. **Prediction Generation**: 5-year forecasts created with volatility modeling
4. **CSV Export**: Predictions saved and copied to frontend
5. **Frontend Display**: Interactive charts show predictions across timeframes

## Model Performance
The system provides comprehensive evaluation including:
- **Validation Metrics**: RMSE, MAE on 3-year test data
- **Final Model Metrics**: Performance on complete dataset
- **Visualization**: Training curves, prediction plots, residual analysis
- **Files Generated**: Multiple analysis plots saved in `AI/model/`

## Troubleshooting

### Missing Prediction Data
If the frontend shows "Prediction data not available":
1. Run: `python AI/bitcoin_price_prediction_using_lstm.py`
2. Wait for training and prediction generation to complete
3. Refresh the frontend page

### Model Training Issues
- Ensure TensorFlow is properly installed
- Check that historical data file exists: `Data/Bitcoin Historical Data.csv`
- For GPU acceleration, ensure CUDA and cuDNN are configured
- Monitor console output for detailed error messages

### Frontend Issues
- Ensure CSV file exists: `Frontend/public/Data/bitcoin_predictions.csv`
- Check browser console for CSV loading errors
- Verify Node.js dependencies are installed: `npm install`

## Contributing
1. Fork the repository
2. Create a feature branch
3. Test with both training script and frontend
4. Submit a pull request

## License
This project is for educational and hackathon purposes.

## Disclaimer
This prediction model is for educational purposes only and should not be used as financial advice. Cryptocurrency investments carry significant risk.
