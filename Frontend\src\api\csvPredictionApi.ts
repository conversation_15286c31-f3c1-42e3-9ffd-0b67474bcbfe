import { PredictionResponse } from '../types';

// CSV parsing utility
const parseCSV = (csvText: string): { dates: string[], prices: number[] } => {
  const lines = csvText.trim().split('\n');
  const headers = lines[0].split(',');
  
  // Find column indices
  const dateIndex = headers.findIndex(h => h.toLowerCase().includes('date'));
  const priceIndex = headers.findIndex(h => h.toLowerCase().includes('price'));
  
  if (dateIndex === -1 || priceIndex === -1) {
    throw new Error('CSV must contain Date and Predicted_Price columns');
  }
  
  const dates: string[] = [];
  const prices: number[] = [];
  
  // Parse data rows (skip header)
  for (let i = 1; i < lines.length; i++) {
    const row = lines[i].split(',');
    if (row.length >= Math.max(dateIndex, priceIndex) + 1) {
      dates.push(row[dateIndex].trim());
      prices.push(parseFloat(row[priceIndex].trim()));
    }
  }
  
  return { dates, prices };
};

// Function to load CSV data from the public folder
const loadCSVData = async (): Promise<{ dates: string[], prices: number[] }> => {
  try {
    console.log('Loading prediction data from CSV file...');
    
    // Try to fetch the CSV file from the public folder
    const response = await fetch('/Data/bitcoin_predictions.csv');
    
    if (!response.ok) {
      throw new Error(`Failed to load CSV file: ${response.status} ${response.statusText}`);
    }
    
    const csvText = await response.text();
    const data = parseCSV(csvText);
    
    console.log(`Loaded ${data.dates.length} predictions from CSV`);
    console.log(`Date range: ${data.dates[0]} to ${data.dates[data.dates.length - 1]}`);
    
    return data;
  } catch (error) {
    console.error('Error loading CSV data:', error);
    throw error;
  }
};

// Function to filter data based on requested timeframe
const filterDataByDays = (data: { dates: string[], prices: number[] }, days: number) => {
  if (days >= data.dates.length) {
    return data; // Return all data if requested days exceed available data
  }
  
  return {
    dates: data.dates.slice(0, days),
    prices: data.prices.slice(0, days)
  };
};

// Main function to fetch predictions (replaces the old API call)
export const fetchPredictionFromCSV = async (days: number): Promise<PredictionResponse> => {
  try {
    console.log(`Fetching predictions for ${days} days from CSV...`);
    
    // Load all data from CSV
    const allData = await loadCSVData();
    
    // Filter data based on requested timeframe
    const filteredData = filterDataByDays(allData, days);
    
    // Format response to match the expected PredictionResponse interface
    const response: PredictionResponse = {
      [`${days}_days`]: {
        dates: filteredData.dates,
        predicted_prices: filteredData.prices
      }
    };
    
    console.log(`Successfully loaded ${filteredData.dates.length} predictions for ${days} days`);
    return response;
    
  } catch (error: any) {
    console.error('Error fetching prediction data from CSV:', error);
    
    // Provide helpful error messages
    if (error.message.includes('Failed to load CSV file')) {
      throw new Error(
        'Prediction data not found. Please run the AI training script first: ' +
        'python AI/bitcoin_price_prediction_using_lstm.py'
      );
    }
    
    throw new Error(`Failed to load prediction data: ${error.message}`);
  }
};

// Function to check if CSV data is available
export const checkCSVDataAvailability = async (): Promise<boolean> => {
  try {
    const response = await fetch('/Data/bitcoin_predictions.csv', { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

// Function to get data statistics
export const getDataStatistics = async (): Promise<{
  totalPredictions: number;
  dateRange: { start: string; end: string };
  lastUpdated?: string;
}> => {
  const data = await loadCSVData();
  
  return {
    totalPredictions: data.dates.length,
    dateRange: {
      start: data.dates[0],
      end: data.dates[data.dates.length - 1]
    }
  };
};
