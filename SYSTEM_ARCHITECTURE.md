# Bitcoin Price Prediction System Architecture

## Overview
This system uses an Advanced Multivariate LSTM model to predict Bitcoin prices and serves them directly to the frontend via CSV files, eliminating the need for a backend API server.

## System Components

### 1. AI Training Script (`AI/bitcoin_price_prediction_using_lstm.py`)
**Purpose**: Train the LSTM model and generate future predictions

**Features**:
- Advanced Multivariate LSTM with Price + Volume data
- 3-step training process:
  1. Validation on latest 3 years of data
  2. Final training on complete historical dataset
  3. Generate 5-year future predictions (1825 days)
- Automatic CSV generation for frontend consumption
- Comprehensive model evaluation and visualization

**Usage**:
```bash
python AI/bitcoin_price_prediction_using_lstm.py
```

**Outputs**:
- `AI/model/bitcoin_advanced_multivariate_lstm.keras` - Trained model
- `AI/model/bitcoin_price_scaler.save` - Price data scaler
- `AI/model/bitcoin_volume_scaler.save` - Volume data scaler
- `Data/bitcoin_predictions.csv` - 5-year daily predictions
- Various visualization plots

### 2. Frontend Application (`Frontend/`)
**Purpose**: Display Bitcoin price predictions in an interactive chart

**Features**:
- React + TypeScript application
- ECharts for data visualization
- Multiple timeframe views (1M, 6M, 1Y, 3Y, 5Y)
- Direct CSV data loading (no API calls)
- Automatic data availability checking

**Key Files**:
- `src/components/BitcoinPriceChart.tsx` - Main chart component
- `src/api/csvPredictionApi.ts` - CSV data loading utility
- `public/Data/bitcoin_predictions.csv` - Prediction data file

### 3. Data Pipeline
**Input**: `Data/Bitcoin Historical Data.csv` (historical Bitcoin data)
**Processing**: Advanced Multivariate LSTM model training
**Output**: `Data/bitcoin_predictions.csv` (future predictions)

## Workflow

### Step 1: Generate Predictions
```bash
# Run the AI training script
python AI/bitcoin_price_prediction_using_lstm.py
```

This will:
1. Load historical Bitcoin data
2. Train the Advanced Multivariate LSTM model
3. Generate 1825 days (5 years) of future predictions
4. Save predictions to `Data/bitcoin_predictions.csv`
5. Copy the CSV to `Frontend/public/Data/` for frontend access

### Step 2: View Predictions
```bash
# Start the frontend application
cd Frontend
npm start
```

The frontend will:
1. Check for CSV data availability
2. Load predictions directly from the CSV file
3. Display interactive charts for different timeframes
4. Show helpful error messages if data is not available

## CSV Data Format

The prediction CSV file contains:
```csv
Date,Predicted_Price
2025-06-11,110000.00
2025-06-12,110500.00
...
```

- **Date**: YYYY-MM-DD format
- **Predicted_Price**: Bitcoin price in USD (2 decimal places)
- **Total Records**: 1825 (5 years of daily predictions)

## Advantages of This Architecture

### 1. **Simplicity**
- No backend server required
- Direct file-based data transfer
- Reduced system complexity

### 2. **Performance**
- No API latency
- Fast CSV parsing
- Efficient data loading

### 3. **Reliability**
- No server downtime issues
- Data persists locally
- Offline-capable frontend

### 4. **Scalability**
- Easy to update predictions (just re-run the script)
- No concurrent user limitations
- Simple deployment

### 5. **Development**
- Easier debugging
- No API endpoint management
- Clear data flow

## Error Handling

### Missing CSV Data
If the CSV file is not found, the frontend displays:
- Clear error message
- Instructions to run the training script
- Refresh button to check for new data

### Invalid Data Format
The CSV parser includes validation for:
- Required columns (Date, Predicted_Price)
- Data type validation
- Empty file handling

## Timeframe Support

The system supports multiple prediction timeframes:
- **1M**: 30 days
- **6M**: 180 days  
- **1Y**: 365 days
- **3Y**: 1095 days
- **5Y**: 1825 days (full dataset)

## Model Details

### Advanced Multivariate LSTM Architecture
- **Input Features**: Price + Volume (normalized)
- **Layers**: 3 LSTM layers (64 units each) + Dense layer
- **Regularization**: Dropout (0.2) for overfitting prevention
- **Optimizer**: Nadam for better convergence
- **Window Size**: 60 days for sequence prediction

### Training Strategy
1. **Validation Phase**: Test on latest 3 years of data
2. **Final Training**: Use complete historical dataset
3. **Prediction Generation**: Create 5-year future forecasts

## Maintenance

### Updating Predictions
To generate new predictions with latest data:
1. Update `Data/Bitcoin Historical Data.csv` with recent data
2. Run `python AI/bitcoin_price_prediction_using_lstm.py`
3. Refresh the frontend to see updated predictions

### Model Retraining
The model automatically retrains on the complete dataset each time the script runs, ensuring predictions use the most recent patterns and data.

## File Structure
```
├── AI/
│   ├── bitcoin_price_prediction_using_lstm.py
│   └── model/
│       ├── bitcoin_advanced_multivariate_lstm.keras
│       ├── bitcoin_price_scaler.save
│       └── bitcoin_volume_scaler.save
├── Data/
│   ├── Bitcoin Historical Data.csv
│   └── bitcoin_predictions.csv
├── Frontend/
│   ├── public/Data/bitcoin_predictions.csv
│   └── src/
│       ├── components/BitcoinPriceChart.tsx
│       └── api/csvPredictionApi.ts
└── SYSTEM_ARCHITECTURE.md
```

This architecture provides a robust, efficient, and maintainable Bitcoin price prediction system without the complexity of a backend API server.
