import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import { 
  <PERSON><PERSON><PERSON>r, 
  ChartHeader, 
  ChartTitle, 
  TimeFrameSelector, 
  TimeFrameButton, 
  Disclaimer,
  LoadingSpinner
} from '../styles/StyledComponents';
import { fetchPredictionFromCSV, checkCSVDataAvailability } from '../api/csvPredictionApi';
import { TimeFrame, TimeFrameMapping, ChartData } from '../types';

const timeFrameMap: TimeFrameMapping = {
  '1m': 30,
  '6m': 180,
  '1y': 365,
  '3y': 1095,
  '5y': 1825, // Added 5-year option to match the generated predictions
};

const BitcoinPriceChart: React.FC = () => {
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>('1m');
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [csvAvailable, setCsvAvailable] = useState<boolean>(false);

  // Check CSV availability on component mount
  useEffect(() => {
    const checkAvailability = async () => {
      const available = await checkCSVDataAvailability();
      setCsvAvailable(available);
    };
    checkAvailability();
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const days = timeFrameMap[selectedTimeFrame];
        console.log(`Loading data for ${selectedTimeFrame} (${days} days)`);

        // Check if CSV data is available
        if (!csvAvailable) {
          throw new Error('Prediction data not available. Please run the AI training script first.');
        }

        console.log('Fetching data from CSV file');
        const data = await fetchPredictionFromCSV(days);

        const key = `${days}_days`;

        if (data && data[key]) {
          console.log(`Successfully loaded data for ${days} days:`, data[key]);
          setChartData({
            dates: data[key].dates,
            prices: data[key].predicted_prices,
          });
        } else {
          console.error('Invalid data format:', data);
          throw new Error('Invalid data format received from CSV');
        }
      } catch (err: any) {
        console.error('Error loading chart data:', err);
        setError(err.message || 'Failed to load prediction data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (csvAvailable) {
      loadData();
    } else if (csvAvailable === false) {
      // CSV check completed but file not available
      setLoading(false);
      setError('Prediction data not available. Please run the AI training script first: python AI/bitcoin_price_prediction_using_lstm.py');
    }
  }, [selectedTimeFrame, csvAvailable]);

  const getOption = () => {
    if (!chartData) return {};

    return {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(13, 17, 23, 0.9)',
        borderColor: '#30363d',
        textStyle: {
          color: '#e6e8ea',
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const date = chartData.dates[dataIndex];
          const price = chartData.prices[dataIndex].toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
          });
          return `<strong>${date}</strong><br/>Predicted Price: ${price}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: chartData.dates,
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: string) => {
            const date = new Date(value);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          },
        },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#21262d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: number) => {
            return '$' + value.toLocaleString();
          },
        },
        scale: true,
        min: (value: { min: number }) => {
          // Set min to ~20% below the minimum value
          return Math.floor(value.min * 0.8);
        },
        max: (value: { max: number }) => {
          // Set max to ~20% above the maximum value
          return Math.ceil(value.max * 1.2);
        },
        splitNumber: 20,
        minInterval: 500
      },
      series: [
        {
          name: 'BTC Price Prediction',
          type: 'line',
          data: chartData.prices,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: '#f7931a',
            width: 3,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(247, 147, 26, 0.5)',
                },
                {
                  offset: 1,
                  color: 'rgba(247, 147, 26, 0.05)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  const handleTimeFrameChange = (timeFrame: TimeFrame) => {
    setSelectedTimeFrame(timeFrame);
  };

  const renderErrorMessage = () => {
    const isDataNotAvailable = error?.includes('not available') || error?.includes('training script');

    return (
      <div style={{
        height: '400px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#f85149',
        textAlign: 'center',
        padding: '0 20px'
      }}>
        <div style={{ fontSize: '1.2rem', marginBottom: '10px' }}>
          {error}
        </div>

        {isDataNotAvailable && (
          <div style={{
            color: '#8b949e',
            fontSize: '0.9rem',
            marginTop: '20px',
            maxWidth: '600px',
            textAlign: 'left',
            background: 'rgba(255,255,255,0.05)',
            padding: '15px',
            borderRadius: '5px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>To generate prediction data:</div>
            <ol style={{ paddingLeft: '20px', margin: 0 }}>
              <li>Open a terminal/command prompt</li>
              <li>Navigate to the project directory</li>
              <li>Run: <code style={{ background: '#21262d', padding: '3px 5px', borderRadius: '3px' }}>python AI/bitcoin_price_prediction_using_lstm.py</code></li>
              <li>Wait for the training and prediction generation to complete</li>
              <li>Refresh this page to load the generated predictions</li>
            </ol>
            <div style={{ marginTop: '10px', fontSize: '0.8rem', color: '#6e7681' }}>
              This will train the AI model and generate 5 years of Bitcoin price predictions.
            </div>
          </div>
        )}

        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '15px',
            padding: '8px 15px',
            backgroundColor: 'var(--accent)',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Page
        </button>
      </div>
    );
  };

  return (
    <ChartContainer>
      <ChartHeader>
        <ChartTitle>Bitcoin Price Prediction</ChartTitle>
        <TimeFrameSelector>
          <TimeFrameButton 
            active={selectedTimeFrame === '1m'} 
            onClick={() => handleTimeFrameChange('1m')}
          >
            1M
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === '6m'} 
            onClick={() => handleTimeFrameChange('6m')}
          >
            6M
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === '1y'} 
            onClick={() => handleTimeFrameChange('1y')}
          >
            1Y
          </TimeFrameButton>
          <TimeFrameButton
            active={selectedTimeFrame === '3y'}
            onClick={() => handleTimeFrameChange('3y')}
          >
            3Y
          </TimeFrameButton>
          <TimeFrameButton
            active={selectedTimeFrame === '5y'}
            onClick={() => handleTimeFrameChange('5y')}
          >
            5Y
          </TimeFrameButton>
        </TimeFrameSelector>
      </ChartHeader>
      
      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        renderErrorMessage()
      ) : (
        <ReactECharts 
          option={getOption()} 
          style={{ height: '600px' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={true}
        />
      )}
      <Disclaimer>
        * Predictions generated by Advanced Multivariate LSTM model. Not financial advice.
        <br />
        * Data updates when you run: python AI/bitcoin_price_prediction_using_lstm.py
      </Disclaimer>
    </ChartContainer>
  );
};

export default BitcoinPriceChart; 